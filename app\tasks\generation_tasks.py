"""
Celery tasks for video and image generation
"""
import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from celery import current_task
from sqlalchemy.orm import Session

from app.celery_app import celery_app
from app.database import SessionLocal
from app.models.database import Task, TaskStatus, FileRecord
from app.models.liveportrait_model import liveportrait_model
from app.services.file_service import file_service
from app.logging_config import app_logger
from app.exceptions import ModelError, TaskError


def get_db_session() -> Session:
    """Get database session for tasks"""
    return SessionLocal()


def update_task_status(
    db: Session,
    task_id: str,
    status: TaskStatus,
    progress: float = None,
    result: Dict[str, Any] = None,
    error_message: str = None
):
    """Update task status in database"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        if task:
            task.status = status
            if progress is not None:
                task.progress = progress
            if result is not None:
                task.result = result
            if error_message is not None:
                task.error_message = error_message
            
            if status == TaskStatus.PROCESSING and not task.started_at:
                task.started_at = datetime.utcnow()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task.completed_at = datetime.utcnow()
            
            db.commit()
            app_logger.info(f"Task {task_id} status updated to {status}")
    except Exception as e:
        app_logger.error(f"Failed to update task status: {e}")
        db.rollback()


@celery_app.task(bind=True)
def generate_portrait_animation(
    self,
    task_id: str,
    source_file_id: str,
    driving_file_id: str,
    parameters: Dict[str, Any] = None
) -> Dict[str, Any]:
    """Generate portrait animation task"""
    
    db = get_db_session()
    parameters = parameters or {}
    
    try:
        app_logger.info(f"Starting portrait animation task {task_id}")
        
        # Update task status to processing
        update_task_status(db, task_id, TaskStatus.PROCESSING, progress=0.0)
        
        # Get file records
        source_file = db.query(FileRecord).filter(FileRecord.id == source_file_id).first()
        driving_file = db.query(FileRecord).filter(FileRecord.id == driving_file_id).first()
        
        if not source_file or not driving_file:
            raise TaskError("Source or driving file not found")
        
        # Check if files exist
        if not file_service.file_exists(source_file):
            raise TaskError(f"Source file not found: {source_file.file_path}")
        
        if not file_service.file_exists(driving_file):
            raise TaskError(f"Driving file not found: {driving_file.file_path}")
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 10})
        update_task_status(db, task_id, TaskStatus.PROCESSING, progress=10.0)
        
        # Generate output filename
        output_filename = f"{task_id}_portrait_animation.mp4"
        output_path = file_service.get_output_path(task_id, output_filename)
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 20})
        update_task_status(db, task_id, TaskStatus.PROCESSING, progress=20.0)
        
        # Generate animation
        result = liveportrait_model.generate_portrait_animation(
            source_image_path=source_file.file_path,
            driving_video_path=driving_file.file_path,
            output_path=str(output_path),
            **parameters
        )
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 90})
        update_task_status(db, task_id, TaskStatus.PROCESSING, progress=90.0)
        
        # Create output file record
        output_file = FileRecord(
            id=uuid.uuid4(),
            filename=output_filename,
            original_filename=output_filename,
            file_path=str(output_path),
            file_size=output_path.stat().st_size if output_path.exists() else 0,
            mime_type='video/mp4',
            file_type='video',
            metadata=result.get('metadata', {}),
            user_id=source_file.user_id
        )
        
        db.add(output_file)
        db.commit()
        
        # Update task with result
        task_result = {
            'output_file_id': str(output_file.id),
            'output_path': str(output_path),
            'metadata': result.get('metadata', {})
        }
        
        # Update task in database
        task = db.query(Task).filter(Task.id == task_id).first()
        if task:
            task.output_file_id = output_file.id
        
        update_task_status(db, task_id, TaskStatus.COMPLETED, progress=100.0, result=task_result)
        
        app_logger.info(f"Portrait animation task {task_id} completed successfully")
        return task_result
        
    except Exception as e:
        app_logger.error(f"Portrait animation task {task_id} failed: {e}")
        error_message = str(e)
        update_task_status(db, task_id, TaskStatus.FAILED, error_message=error_message)
        raise TaskError(error_message)
    
    finally:
        db.close()


@celery_app.task(bind=True)
def generate_animal_animation(
    self,
    task_id: str,
    source_file_id: str,
    driving_file_id: str,
    parameters: Dict[str, Any] = None
) -> Dict[str, Any]:
    """Generate animal animation task"""
    
    db = get_db_session()
    parameters = parameters or {}
    
    try:
        app_logger.info(f"Starting animal animation task {task_id}")
        
        # Update task status to processing
        update_task_status(db, task_id, TaskStatus.PROCESSING, progress=0.0)
        
        # Get file records
        source_file = db.query(FileRecord).filter(FileRecord.id == source_file_id).first()
        driving_file = db.query(FileRecord).filter(FileRecord.id == driving_file_id).first()
        
        if not source_file or not driving_file:
            raise TaskError("Source or driving file not found")
        
        # Check if files exist
        if not file_service.file_exists(source_file):
            raise TaskError(f"Source file not found: {source_file.file_path}")
        
        if not file_service.file_exists(driving_file):
            raise TaskError(f"Driving file not found: {driving_file.file_path}")
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 10})
        update_task_status(db, task_id, TaskStatus.PROCESSING, progress=10.0)
        
        # Generate output filename
        output_filename = f"{task_id}_animal_animation.mp4"
        output_path = file_service.get_output_path(task_id, output_filename)
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 20})
        update_task_status(db, task_id, TaskStatus.PROCESSING, progress=20.0)
        
        # Generate animation
        result = liveportrait_model.generate_animal_animation(
            source_image_path=source_file.file_path,
            driving_data_path=driving_file.file_path,
            output_path=str(output_path),
            **parameters
        )
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'progress': 90})
        update_task_status(db, task_id, TaskStatus.PROCESSING, progress=90.0)
        
        # Create output file record
        output_file = FileRecord(
            id=uuid.uuid4(),
            filename=output_filename,
            original_filename=output_filename,
            file_path=str(output_path),
            file_size=output_path.stat().st_size if output_path.exists() else 0,
            mime_type='video/mp4',
            file_type='video',
            metadata=result.get('metadata', {}),
            user_id=source_file.user_id
        )
        
        db.add(output_file)
        db.commit()
        
        # Update task with result
        task_result = {
            'output_file_id': str(output_file.id),
            'output_path': str(output_path),
            'metadata': result.get('metadata', {})
        }
        
        # Update task in database
        task = db.query(Task).filter(Task.id == task_id).first()
        if task:
            task.output_file_id = output_file.id
        
        update_task_status(db, task_id, TaskStatus.COMPLETED, progress=100.0, result=task_result)
        
        app_logger.info(f"Animal animation task {task_id} completed successfully")
        return task_result
        
    except Exception as e:
        app_logger.error(f"Animal animation task {task_id} failed: {e}")
        error_message = str(e)
        update_task_status(db, task_id, TaskStatus.FAILED, error_message=error_message)
        raise TaskError(error_message)
    
    finally:
        db.close()


@celery_app.task
def cleanup_old_files(days: int = 7) -> Dict[str, Any]:
    """Clean up old files task"""
    try:
        app_logger.info(f"Starting cleanup of files older than {days} days")
        deleted_count = file_service.cleanup_old_files(days)
        
        result = {
            'deleted_count': deleted_count,
            'days': days,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        app_logger.info(f"Cleanup completed: {deleted_count} files deleted")
        return result
        
    except Exception as e:
        app_logger.error(f"Cleanup task failed: {e}")
        raise TaskError(f"Cleanup failed: {e}")
