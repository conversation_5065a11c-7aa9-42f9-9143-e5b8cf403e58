"""
Configuration management for LivePortrait API
"""
import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from pathlib import Path


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    app_name: str = "LivePortrait API"
    app_version: str = "1.0.0"
    debug: bool = False
    secret_key: str = "your-secret-key-change-in-production"
    api_v1_str: str = "/api/v1"
    
    # Database
    database_url: str = "postgresql://liveportrait_user:liveportrait_password@localhost:5432/liveportrait"
    database_host: str = "localhost"
    database_port: int = 5432
    database_name: str = "liveportrait"
    database_user: str = "liveportrait_user"
    database_password: str = "liveportrait_password"
    
    # Redis
    redis_url: str = "redis://localhost:6379/0"
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    
    # Celery
    celery_broker_url: str = "redis://localhost:6379/1"
    celery_result_backend: str = "redis://localhost:6379/2"
    
    # File Storage
    upload_dir: str = "./uploads"
    output_dir: str = "./outputs"
    max_file_size: str = "100MB"
    allowed_image_extensions: List[str] = ["jpg", "jpeg", "png", "bmp", "tiff"]
    allowed_video_extensions: List[str] = ["mp4", "avi", "mov", "mkv"]
    
    # LivePortrait Model
    model_weights_dir: str = "./pretrained_weights"
    device: str = "cuda"
    model_cache_size: int = 2
    enable_torch_compile: bool = False
    
    # Security
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"
    
    # Logging
    log_level: str = "INFO"
    log_file: str = "./logs/app.log"
    log_rotation: str = "10MB"
    log_retention: str = "30 days"
    
    # Performance
    max_workers: int = 4
    worker_timeout: int = 300
    max_concurrent_tasks: int = 10
    
    # Cloud Storage (Optional)
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_region: str = "us-east-1"
    s3_bucket: Optional[str] = None
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    # CORS
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["GET", "POST", "PUT", "DELETE"]
    cors_allow_headers: List[str] = ["*"]
    
    @validator("upload_dir", "output_dir", "model_weights_dir")
    def create_directories(cls, v):
        """Create directories if they don't exist"""
        Path(v).mkdir(parents=True, exist_ok=True)
        return v
    
    @validator("max_file_size")
    def parse_file_size(cls, v):
        """Parse file size string to bytes"""
        if isinstance(v, str):
            v = v.upper()
            if v.endswith("MB"):
                return int(v[:-2]) * 1024 * 1024
            elif v.endswith("GB"):
                return int(v[:-2]) * 1024 * 1024 * 1024
            elif v.endswith("KB"):
                return int(v[:-2]) * 1024
            else:
                return int(v)
        return v
    
    @property
    def database_url_sync(self) -> str:
        """Synchronous database URL for SQLAlchemy"""
        return self.database_url.replace("postgresql://", "postgresql+psycopg2://")
    
    @property
    def database_url_async(self) -> str:
        """Asynchronous database URL for SQLAlchemy"""
        return self.database_url.replace("postgresql://", "postgresql+asyncpg://")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()

# Create necessary directories
Path(settings.upload_dir).mkdir(parents=True, exist_ok=True)
Path(settings.output_dir).mkdir(parents=True, exist_ok=True)
Path(settings.model_weights_dir).mkdir(parents=True, exist_ok=True)
Path("./logs").mkdir(parents=True, exist_ok=True)
