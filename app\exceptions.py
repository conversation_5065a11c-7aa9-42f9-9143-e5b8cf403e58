"""
Custom exceptions for LivePortrait API
"""
from typing import Any, Dict, Optional


class LivePortraitException(Exception):
    """Base exception for LivePortrait API"""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(LivePortraitException):
    """Validation error"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=422, details=details)


class FileNotFoundError(LivePortraitException):
    """File not found error"""
    
    def __init__(self, message: str = "File not found"):
        super().__init__(message, status_code=404)


class FileUploadError(LivePortraitException):
    """File upload error"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=400, details=details)


class ModelError(LivePortraitException):
    """Model processing error"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)


class TaskNotFoundError(LivePortraitException):
    """Task not found error"""
    
    def __init__(self, task_id: str):
        super().__init__(f"Task {task_id} not found", status_code=404)


class TaskError(LivePortraitException):
    """Task processing error"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)


class AuthenticationError(LivePortraitException):
    """Authentication error"""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, status_code=401)


class AuthorizationError(LivePortraitException):
    """Authorization error"""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(message, status_code=403)


class RateLimitError(LivePortraitException):
    """Rate limit exceeded error"""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, status_code=429)


class StorageError(LivePortraitException):
    """Storage operation error"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=500, details=details)
