"""
Celery application configuration
"""
from celery import Celery
from app.config import settings

# Create Celery app
celery_app = Celery(
    "liveportrait",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=['app.tasks.generation_tasks']
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    result_expires=3600,  # 1 hour
    task_routes={
        'app.tasks.generation_tasks.generate_portrait_animation': {'queue': 'generation'},
        'app.tasks.generation_tasks.generate_animal_animation': {'queue': 'generation'},
        'app.tasks.generation_tasks.cleanup_old_files': {'queue': 'maintenance'},
    },
    beat_schedule={
        'cleanup-old-files': {
            'task': 'app.tasks.generation_tasks.cleanup_old_files',
            'schedule': 24 * 60 * 60,  # Daily
        },
    },
)
