"""
File storage and management service
"""
import os
import uuid
import shutil
import mimetypes
from pathlib import Path
from typing import Optional, Dict, Any, BinaryIO, List
import aiofiles
from PIL import Image
import cv2

from app.config import settings
from app.logging_config import app_logger
from app.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON>rror, FileNotFoundError, StorageError
from app.models.database import FileRecord


class FileService:
    """File storage and management service"""
    
    def __init__(self):
        self.upload_dir = Path(settings.upload_dir)
        self.output_dir = Path(settings.output_dir)
        self.max_file_size = settings.max_file_size
        self.allowed_image_extensions = settings.allowed_image_extensions
        self.allowed_video_extensions = settings.allowed_video_extensions
        
        # Create directories if they don't exist
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def save_uploaded_file(
        self,
        file_content: bytes,
        filename: str,
        user_id: Optional[str] = None
    ) -> FileRecord:
        """Save uploaded file and create file record"""
        
        try:
            # Validate file
            self._validate_file(file_content, filename)
            
            # Generate unique filename
            file_id = uuid.uuid4()
            file_extension = Path(filename).suffix.lower()
            unique_filename = f"{file_id}{file_extension}"
            
            # Determine file type
            file_type = self._get_file_type(filename)
            mime_type = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
            
            # Create subdirectory based on file type
            type_dir = self.upload_dir / file_type
            type_dir.mkdir(exist_ok=True)
            
            # Save file
            file_path = type_dir / unique_filename
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            # Get file metadata
            metadata = await self._get_file_metadata(file_path, file_type)
            
            # Create file record
            file_record = FileRecord(
                id=file_id,
                filename=unique_filename,
                original_filename=filename,
                file_path=str(file_path),
                file_size=len(file_content),
                mime_type=mime_type,
                file_type=file_type,
                metadata=metadata,
                user_id=user_id
            )
            
            app_logger.info(f"File saved: {filename} -> {file_path}")
            return file_record
            
        except Exception as e:
            app_logger.error(f"Failed to save file {filename}: {e}")
            raise FileUploadError(f"Failed to save file: {e}")
    
    def _validate_file(self, file_content: bytes, filename: str):
        """Validate uploaded file"""
        
        # Check file size
        if len(file_content) > self.max_file_size:
            raise FileUploadError(
                f"File size {len(file_content)} exceeds maximum {self.max_file_size}"
            )
        
        # Check file extension
        file_extension = Path(filename).suffix.lower().lstrip('.')
        allowed_extensions = self.allowed_image_extensions + self.allowed_video_extensions
        
        if file_extension not in allowed_extensions:
            raise FileUploadError(
                f"File extension '{file_extension}' not allowed. "
                f"Allowed: {', '.join(allowed_extensions)}"
            )
        
        # Check if file is empty
        if len(file_content) == 0:
            raise FileUploadError("File is empty")
    
    def _get_file_type(self, filename: str) -> str:
        """Determine file type based on extension"""
        file_extension = Path(filename).suffix.lower().lstrip('.')
        
        if file_extension in self.allowed_image_extensions:
            return 'image'
        elif file_extension in self.allowed_video_extensions:
            return 'video'
        else:
            return 'data'
    
    async def _get_file_metadata(self, file_path: Path, file_type: str) -> Dict[str, Any]:
        """Extract file metadata"""
        metadata = {}
        
        try:
            if file_type == 'image':
                metadata = await self._get_image_metadata(file_path)
            elif file_type == 'video':
                metadata = await self._get_video_metadata(file_path)
        except Exception as e:
            app_logger.warning(f"Failed to extract metadata for {file_path}: {e}")
        
        return metadata
    
    async def _get_image_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Extract image metadata"""
        try:
            with Image.open(file_path) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode,
                    'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                }
        except Exception as e:
            app_logger.warning(f"Failed to get image metadata: {e}")
            return {}
    
    async def _get_video_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Extract video metadata"""
        try:
            cap = cv2.VideoCapture(str(file_path))
            if not cap.isOpened():
                return {}
            
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            
            cap.release()
            
            return {
                'width': width,
                'height': height,
                'fps': fps,
                'frame_count': frame_count,
                'duration': duration
            }
        except Exception as e:
            app_logger.warning(f"Failed to get video metadata: {e}")
            return {}
    
    def get_file_path(self, file_record: FileRecord) -> Path:
        """Get file path from file record"""
        return Path(file_record.file_path)
    
    def file_exists(self, file_record: FileRecord) -> bool:
        """Check if file exists on disk"""
        return Path(file_record.file_path).exists()
    
    async def delete_file(self, file_record: FileRecord) -> bool:
        """Delete file from disk"""
        try:
            file_path = Path(file_record.file_path)
            if file_path.exists():
                file_path.unlink()
                app_logger.info(f"File deleted: {file_path}")
                return True
            return False
        except Exception as e:
            app_logger.error(f"Failed to delete file {file_record.file_path}: {e}")
            raise StorageError(f"Failed to delete file: {e}")
    
    async def copy_file(self, source_path: Path, destination_path: Path) -> bool:
        """Copy file from source to destination"""
        try:
            destination_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source_path, destination_path)
            app_logger.info(f"File copied: {source_path} -> {destination_path}")
            return True
        except Exception as e:
            app_logger.error(f"Failed to copy file: {e}")
            raise StorageError(f"Failed to copy file: {e}")
    
    def get_output_path(self, task_id: str, filename: str) -> Path:
        """Generate output file path for a task"""
        task_dir = self.output_dir / str(task_id)
        task_dir.mkdir(parents=True, exist_ok=True)
        return task_dir / filename
    
    def cleanup_old_files(self, days: int = 7) -> int:
        """Clean up files older than specified days"""
        import time
        
        current_time = time.time()
        cutoff_time = current_time - (days * 24 * 60 * 60)
        deleted_count = 0
        
        try:
            for directory in [self.upload_dir, self.output_dir]:
                for file_path in directory.rglob('*'):
                    if file_path.is_file():
                        file_mtime = file_path.stat().st_mtime
                        if file_mtime < cutoff_time:
                            try:
                                file_path.unlink()
                                deleted_count += 1
                                app_logger.info(f"Cleaned up old file: {file_path}")
                            except Exception as e:
                                app_logger.warning(f"Failed to delete old file {file_path}: {e}")
            
            app_logger.info(f"Cleanup completed: {deleted_count} files deleted")
            return deleted_count
            
        except Exception as e:
            app_logger.error(f"Cleanup failed: {e}")
            return deleted_count


# Global file service instance
file_service = FileService()
