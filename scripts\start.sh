#!/bin/bash

# LivePortrait API startup script

set -e

echo "Starting LivePortrait API..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo "Please edit .env file with your configuration"
fi

# Create necessary directories
mkdir -p uploads outputs logs pretrained_weights

# Check if pretrained weights exist
if [ ! -d "pretrained_weights/liveportrait" ]; then
    echo "Downloading LivePortrait pretrained weights..."
    python -c "
import os
from huggingface_hub import snapshot_download
try:
    snapshot_download(
        repo_id='KwaiVGI/LivePortrait',
        local_dir='./pretrained_weights',
        ignore_patterns=['*.git*', 'README.md', 'docs']
    )
    print('Successfully downloaded LivePortrait weights')
except Exception as e:
    print(f'Failed to download weights: {e}')
    print('Please download manually or check your internet connection')
"
fi

# Start the application
if [ "$1" = "dev" ]; then
    echo "Starting in development mode..."
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
elif [ "$1" = "worker" ]; then
    echo "Starting Celery worker..."
    celery -A app.celery_app worker --loglevel=info --concurrency=2
elif [ "$1" = "flower" ]; then
    echo "Starting Celery Flower..."
    celery -A app.celery_app flower --port=5555
else
    echo "Starting in production mode..."
    gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
fi
