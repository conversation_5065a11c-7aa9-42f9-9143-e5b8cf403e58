"""
Database connection and session management
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from app.config import settings
from app.models.database import Base


# Synchronous database engine
sync_engine = create_engine(
    settings.database_url_sync,
    poolclass=StaticPool,
    pool_pre_ping=True,
    echo=settings.debug
)

# Asynchronous database engine
async_engine = create_async_engine(
    settings.database_url_async,
    poolclass=StaticPool,
    pool_pre_ping=True,
    echo=settings.debug
)

# Session makers
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine
)

AsyncSessionLocal = sessionmaker(
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    bind=async_engine
)


def create_tables():
    """Create database tables"""
    Base.metadata.create_all(bind=sync_engine)


def get_db() -> Session:
    """Get synchronous database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncSession:
    """Get asynchronous database session"""
    async with AsyncSessionLocal() as session:
        yield session
