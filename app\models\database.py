"""
Database models for LivePortrait API
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from sqlalchemy import Column, String, DateTime, Text, Integer, Float, <PERSON><PERSON><PERSON>, JSO<PERSON>
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(str, Enum):
    """Task type enumeration"""
    PORTRAIT_ANIMATION = "portrait_animation"
    ANIMAL_ANIMATION = "animal_animation"
    IMAGE_GENERATION = "image_generation"


class Task(Base):
    """Task model"""
    __tablename__ = "tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_type = Column(String(50), nullable=False)
    status = Column(String(20), default=TaskStatus.PENDING, nullable=False)
    
    # Input files
    source_file_id = Column(UUID(as_uuid=True), nullable=True)
    driving_file_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Output files
    output_file_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Task parameters
    parameters = Column(JSON, default={})
    
    # Progress and results
    progress = Column(Float, default=0.0)
    result = Column(JSON, default={})
    error_message = Column(Text, nullable=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # User information (for future authentication)
    user_id = Column(String(255), nullable=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary"""
        return {
            'id': str(self.id),
            'task_type': self.task_type,
            'status': self.status,
            'source_file_id': str(self.source_file_id) if self.source_file_id else None,
            'driving_file_id': str(self.driving_file_id) if self.driving_file_id else None,
            'output_file_id': str(self.output_file_id) if self.output_file_id else None,
            'parameters': self.parameters,
            'progress': self.progress,
            'result': self.result,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'user_id': self.user_id
        }


class FileRecord(Base):
    """File record model"""
    __tablename__ = "files"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=False)
    file_type = Column(String(20), nullable=False)  # 'image', 'video', 'data'
    
    # File metadata
    metadata = Column(JSON, default={})
    
    # Upload information
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())
    user_id = Column(String(255), nullable=True)
    
    # File status
    is_processed = Column(Boolean, default=False)
    is_deleted = Column(Boolean, default=False)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert file record to dictionary"""
        return {
            'id': str(self.id),
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'mime_type': self.mime_type,
            'file_type': self.file_type,
            'metadata': self.metadata,
            'uploaded_at': self.uploaded_at.isoformat() if self.uploaded_at else None,
            'user_id': self.user_id,
            'is_processed': self.is_processed,
            'is_deleted': self.is_deleted
        }


class User(Base):
    """User model (for future authentication)"""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(100), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    
    # User status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # Usage statistics
    total_tasks = Column(Integer, default=0)
    successful_tasks = Column(Integer, default=0)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary"""
        return {
            'id': str(self.id),
            'username': self.username,
            'email': self.email,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'total_tasks': self.total_tasks,
            'successful_tasks': self.successful_tasks
        }
