version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: liveportrait_postgres
    environment:
      POSTGRES_DB: liveportrait
      POSTGRES_USER: liveportrait_user
      POSTGRES_PASSWORD: liveportrait_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - liveportrait_network

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    container_name: liveportrait_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - liveportrait_network

  # FastAPI Application
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: liveportrait_api
    environment:
      - DATABASE_URL=******************************************************************/liveportrait
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
      - ./pretrained_weights:/app/pretrained_weights
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - liveportrait_network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Celery Worker for background tasks
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: liveportrait_worker
    command: celery -A app.celery_app worker --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=******************************************************************/liveportrait
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
      - ./pretrained_weights:/app/pretrained_weights
    depends_on:
      - postgres
      - redis
    networks:
      - liveportrait_network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Celery Flower for monitoring
  flower:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: liveportrait_flower
    command: celery -A app.celery_app flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - liveportrait_network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: liveportrait_nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./uploads:/var/www/uploads
      - ./outputs:/var/www/outputs
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - api
    networks:
      - liveportrait_network

volumes:
  postgres_data:
  redis_data:

networks:
  liveportrait_network:
    driver: bridge
