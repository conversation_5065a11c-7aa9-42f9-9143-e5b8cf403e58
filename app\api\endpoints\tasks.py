"""
Task management API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.database import get_db
from app.models.database import Task, TaskStatus, TaskType, FileRecord
from app.api.schemas import (
    TaskCreateRequest, TaskResponse, TaskListResponse,
    TaskStatusEnum, TaskTypeEnum, PaginationParams, TaskFilterParams
)
from app.tasks.generation_tasks import generate_portrait_animation, generate_animal_animation
from app.logging_config import app_logger

router = APIRouter()


@router.post("/tasks", response_model=TaskResponse)
async def create_task(
    task_request: TaskCreateRequest,
    db: Session = Depends(get_db)
):
    """Create a new task"""
    try:
        # Validate source file
        source_file = db.query(FileRecord).filter(
            FileRecord.id == task_request.source_file_id
        ).first()
        
        if not source_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Source file not found"
            )
        
        # Validate driving file if provided
        driving_file = None
        if task_request.driving_file_id:
            driving_file = db.query(FileRecord).filter(
                FileRecord.id == task_request.driving_file_id
            ).first()
            
            if not driving_file:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Driving file not found"
                )
        
        # Create task record
        task = Task(
            task_type=task_request.task_type.value,
            source_file_id=task_request.source_file_id,
            driving_file_id=task_request.driving_file_id,
            parameters=task_request.parameters,
            user_id=None  # TODO: Get from authentication
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        # Submit task to Celery
        task_id = str(task.id)
        
        if task_request.task_type == TaskTypeEnum.PORTRAIT_ANIMATION:
            generate_portrait_animation.delay(
                task_id=task_id,
                source_file_id=task_request.source_file_id,
                driving_file_id=task_request.driving_file_id,
                parameters=task_request.parameters
            )
        elif task_request.task_type == TaskTypeEnum.ANIMAL_ANIMATION:
            generate_animal_animation.delay(
                task_id=task_id,
                source_file_id=task_request.source_file_id,
                driving_file_id=task_request.driving_file_id,
                parameters=task_request.parameters
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported task type: {task_request.task_type}"
            )
        
        app_logger.info(f"Task created: {task_id} ({task_request.task_type})")
        
        return TaskResponse(**task.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error creating task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """Get task by ID"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        return TaskResponse(**task.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error getting task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/tasks", response_model=TaskListResponse)
async def list_tasks(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    status: Optional[TaskStatusEnum] = None,
    task_type: Optional[TaskTypeEnum] = None,
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """List tasks with pagination and filtering"""
    try:
        query = db.query(Task)
        
        # Apply filters
        if status:
            query = query.filter(Task.status == status.value)
        
        if task_type:
            query = query.filter(Task.task_type == task_type.value)
        
        if user_id:
            query = query.filter(Task.user_id == user_id)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * size
        tasks = query.offset(offset).limit(size).all()
        
        return TaskListResponse(
            tasks=[TaskResponse(**task.to_dict()) for task in tasks],
            total=total,
            page=page,
            size=size
        )
        
    except Exception as e:
        app_logger.error(f"Error listing tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/tasks/{task_id}")
async def cancel_task(
    task_id: str,
    db: Session = Depends(get_db)
):
    """Cancel a task"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot cancel task with status: {task.status}"
            )
        
        # Update task status
        task.status = TaskStatus.CANCELLED
        db.commit()
        
        # TODO: Cancel Celery task
        
        app_logger.info(f"Task cancelled: {task_id}")
        
        return {"message": "Task cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error cancelling task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/tasks/{task_id}/result")
async def get_task_result(
    task_id: str,
    db: Session = Depends(get_db)
):
    """Get task result"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )
        
        if task.status != TaskStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Task not completed. Current status: {task.status}"
            )
        
        # Get output file info if available
        result = task.result.copy() if task.result else {}
        
        if task.output_file_id:
            output_file = db.query(FileRecord).filter(
                FileRecord.id == task.output_file_id
            ).first()
            
            if output_file:
                result['output_file'] = output_file.to_dict()
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error getting task result: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
