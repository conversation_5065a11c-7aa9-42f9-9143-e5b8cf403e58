#!/bin/bash

# LivePortrait API setup script

set -e

echo "Setting up LivePortrait API..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not installed. Please install Python 3.10 or later."
    exit 1
fi

# Check if pip is installed
if ! command -v pip &> /dev/null; then
    echo "pip is required but not installed. Please install pip."
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo "Please edit .env file with your configuration"
fi

# Create necessary directories
echo "Creating directories..."
mkdir -p uploads outputs logs pretrained_weights

# Download LivePortrait repository if not exists
if [ ! -d "LivePortrait" ]; then
    echo "Cloning LivePortrait repository..."
    git clone https://github.com/KwaiVGI/LivePortrait.git
    
    # Install LivePortrait dependencies
    cd LivePortrait
    pip install -r requirements.txt
    cd ..
fi

# Download pretrained weights
echo "Downloading pretrained weights..."
python -c "
import os
from huggingface_hub import snapshot_download
try:
    snapshot_download(
        repo_id='KwaiVGI/LivePortrait',
        local_dir='./pretrained_weights',
        ignore_patterns=['*.git*', 'README.md', 'docs']
    )
    print('Successfully downloaded LivePortrait weights')
except Exception as e:
    print(f'Failed to download weights: {e}')
    print('Please download manually or check your internet connection')
"

echo "Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your configuration"
echo "2. Start PostgreSQL and Redis services"
echo "3. Run 'python -m alembic upgrade head' to create database tables"
echo "4. Start the API server with 'python app/main.py' or 'uvicorn app.main:app'"
echo "5. Start Celery worker with 'celery -A app.celery_app worker'"
