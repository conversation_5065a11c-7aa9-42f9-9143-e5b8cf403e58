"""
Pydantic schemas for API requests and responses
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum


class TaskStatusEnum(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskTypeEnum(str, Enum):
    """Task type enumeration"""
    PORTRAIT_ANIMATION = "portrait_animation"
    ANIMAL_ANIMATION = "animal_animation"
    IMAGE_GENERATION = "image_generation"


class FileTypeEnum(str, Enum):
    """File type enumeration"""
    IMAGE = "image"
    VIDEO = "video"
    DATA = "data"


# File schemas
class FileUploadResponse(BaseModel):
    """File upload response"""
    id: str
    filename: str
    original_filename: str
    file_size: int
    mime_type: str
    file_type: str
    metadata: Dict[str, Any] = {}
    uploaded_at: datetime


class FileInfoResponse(BaseModel):
    """File information response"""
    id: str
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    mime_type: str
    file_type: str
    metadata: Dict[str, Any] = {}
    uploaded_at: datetime
    is_processed: bool
    is_deleted: bool


# Task schemas
class TaskCreateRequest(BaseModel):
    """Task creation request"""
    task_type: TaskTypeEnum
    source_file_id: str
    driving_file_id: Optional[str] = None
    parameters: Dict[str, Any] = Field(default_factory=dict)


class TaskResponse(BaseModel):
    """Task response"""
    id: str
    task_type: str
    status: str
    source_file_id: Optional[str] = None
    driving_file_id: Optional[str] = None
    output_file_id: Optional[str] = None
    parameters: Dict[str, Any] = {}
    progress: float = 0.0
    result: Dict[str, Any] = {}
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    user_id: Optional[str] = None


class TaskListResponse(BaseModel):
    """Task list response"""
    tasks: List[TaskResponse]
    total: int
    page: int
    size: int


# Generation request schemas
class PortraitAnimationRequest(BaseModel):
    """Portrait animation generation request"""
    source_file_id: str = Field(..., description="Source image file ID")
    driving_file_id: str = Field(..., description="Driving video file ID")
    flag_lip_zero: bool = Field(False, description="Zero out lip motion")
    flag_eye_retargeting: bool = Field(False, description="Enable eye retargeting")
    flag_stitching: bool = Field(True, description="Enable stitching")
    flag_relative: bool = Field(True, description="Use relative motion")
    flag_pasteback: bool = Field(True, description="Enable paste back")
    flag_do_crop: bool = Field(True, description="Enable cropping")
    flag_crop_driving_video: bool = Field(False, description="Auto-crop driving video")


class AnimalAnimationRequest(BaseModel):
    """Animal animation generation request"""
    source_file_id: str = Field(..., description="Source image file ID")
    driving_file_id: str = Field(..., description="Driving data file ID")
    driving_multiplier: float = Field(1.0, description="Driving motion multiplier")
    flag_stitching: bool = Field(False, description="Enable stitching for animals")


# Response schemas
class GenerationResponse(BaseModel):
    """Generation response"""
    task_id: str
    status: str
    message: str


class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str
    timestamp: datetime
    version: str
    model_status: Dict[str, Any] = {}


class ErrorResponse(BaseModel):
    """Error response"""
    error: str
    message: str
    details: Dict[str, Any] = {}
    timestamp: datetime


# Pagination schemas
class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(10, ge=1, le=100, description="Page size")


# Filter schemas
class TaskFilterParams(BaseModel):
    """Task filter parameters"""
    status: Optional[TaskStatusEnum] = None
    task_type: Optional[TaskTypeEnum] = None
    user_id: Optional[str] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
