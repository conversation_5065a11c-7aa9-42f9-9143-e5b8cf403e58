"""
File management API endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.database import FileRecord
from app.services.file_service import file_service
from app.api.schemas import FileUploadResponse, FileInfoResponse
from app.logging_config import app_logger
from app.exceptions import FileUploadError, FileNotFoundError

router = APIRouter()


@router.post("/upload/image", response_model=FileUploadResponse)
async def upload_image(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload an image file"""
    try:
        # Read file content
        file_content = await file.read()
        
        # Save file
        file_record = await file_service.save_uploaded_file(
            file_content=file_content,
            filename=file.filename,
            user_id=None  # TODO: Get from authentication
        )
        
        # Save to database
        db.add(file_record)
        db.commit()
        db.refresh(file_record)
        
        app_logger.info(f"Image uploaded: {file.filename} -> {file_record.id}")
        
        return FileUploadResponse(
            id=str(file_record.id),
            filename=file_record.filename,
            original_filename=file_record.original_filename,
            file_size=file_record.file_size,
            mime_type=file_record.mime_type,
            file_type=file_record.file_type,
            metadata=file_record.metadata,
            uploaded_at=file_record.uploaded_at
        )
        
    except FileUploadError as e:
        app_logger.error(f"File upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        app_logger.error(f"Unexpected error during file upload: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/upload/video", response_model=FileUploadResponse)
async def upload_video(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload a video file"""
    try:
        # Read file content
        file_content = await file.read()
        
        # Save file
        file_record = await file_service.save_uploaded_file(
            file_content=file_content,
            filename=file.filename,
            user_id=None  # TODO: Get from authentication
        )
        
        # Save to database
        db.add(file_record)
        db.commit()
        db.refresh(file_record)
        
        app_logger.info(f"Video uploaded: {file.filename} -> {file_record.id}")
        
        return FileUploadResponse(
            id=str(file_record.id),
            filename=file_record.filename,
            original_filename=file_record.original_filename,
            file_size=file_record.file_size,
            mime_type=file_record.mime_type,
            file_type=file_record.file_type,
            metadata=file_record.metadata,
            uploaded_at=file_record.uploaded_at
        )
        
    except FileUploadError as e:
        app_logger.error(f"File upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        app_logger.error(f"Unexpected error during file upload: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/files/{file_id}", response_model=FileInfoResponse)
async def get_file_info(
    file_id: str,
    db: Session = Depends(get_db)
):
    """Get file information"""
    try:
        file_record = db.query(FileRecord).filter(FileRecord.id == file_id).first()
        
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        return FileInfoResponse(**file_record.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error getting file info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/files/{file_id}/download")
async def download_file(
    file_id: str,
    db: Session = Depends(get_db)
):
    """Download a file"""
    try:
        file_record = db.query(FileRecord).filter(FileRecord.id == file_id).first()
        
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        if not file_service.file_exists(file_record):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found on disk"
            )
        
        return FileResponse(
            path=file_record.file_path,
            filename=file_record.original_filename,
            media_type=file_record.mime_type
        )
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error downloading file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/files/{file_id}")
async def delete_file(
    file_id: str,
    db: Session = Depends(get_db)
):
    """Delete a file"""
    try:
        file_record = db.query(FileRecord).filter(FileRecord.id == file_id).first()
        
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Mark as deleted in database
        file_record.is_deleted = True
        db.commit()
        
        # Delete from disk
        await file_service.delete_file(file_record)
        
        app_logger.info(f"File deleted: {file_id}")
        
        return {"message": "File deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error deleting file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/files", response_model=List[FileInfoResponse])
async def list_files(
    skip: int = 0,
    limit: int = 100,
    file_type: str = None,
    db: Session = Depends(get_db)
):
    """List files"""
    try:
        query = db.query(FileRecord).filter(FileRecord.is_deleted == False)
        
        if file_type:
            query = query.filter(FileRecord.file_type == file_type)
        
        files = query.offset(skip).limit(limit).all()
        
        return [FileInfoResponse(**file_record.to_dict()) for file_record in files]
        
    except Exception as e:
        app_logger.error(f"Error listing files: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
