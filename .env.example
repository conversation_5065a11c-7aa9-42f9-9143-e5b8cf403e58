# Database Configuration
DATABASE_URL=postgresql://liveportrait_user:liveportrait_password@localhost:5432/liveportrait
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=liveportrait
DATABASE_USER=liveportrait_user
DATABASE_PASSWORD=liveportrait_password

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Application Configuration
APP_NAME=LivePortrait API
APP_VERSION=1.0.0
DEBUG=False
SECRET_KEY=your-secret-key-here
API_V1_STR=/api/v1

# File Storage Configuration
UPLOAD_DIR=./uploads
OUTPUT_DIR=./outputs
MAX_FILE_SIZE=100MB
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,bmp,tiff
ALLOWED_VIDEO_EXTENSIONS=mp4,avi,mov,mkv

# LivePortrait Model Configuration
MODEL_WEIGHTS_DIR=./pretrained_weights
DEVICE=cuda
MODEL_CACHE_SIZE=2
ENABLE_TORCH_COMPILE=False

# Security Configuration
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
LOG_ROTATION=10MB
LOG_RETENTION=30 days

# Performance Configuration
MAX_WORKERS=4
WORKER_TIMEOUT=300
MAX_CONCURRENT_TASKS=10

# Cloud Storage (Optional)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=us-east-1
# S3_BUCKET=your-s3-bucket

# Monitoring Configuration
ENABLE_METRICS=True
METRICS_PORT=9090

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=True
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE"]
CORS_ALLOW_HEADERS=["*"]
