# LivePortrait API

基于快手可灵大模型（LivePortrait）的后端API系统，支持人像动画和动物动画生成。

## 功能特性

- 🎭 **人像动画生成**: 基于源图片和驱动视频生成人像动画
- 🐱 **动物动画生成**: 支持动物图片的动画生成
- 🚀 **异步处理**: 基于Celery的异步任务队列
- 📁 **文件管理**: 完整的文件上传、存储和管理系统
- 🐳 **容器化部署**: 支持Docker和Docker Compose部署
- 📊 **任务监控**: 实时任务状态跟踪和进度监控
- 🔧 **RESTful API**: 完整的REST API接口

## 技术栈

- **Web框架**: FastAPI
- **任务队列**: Celery + Redis
- **数据库**: PostgreSQL
- **AI模型**: KwaiVGI/LivePortrait (Hugging Face)
- **容器化**: Docker + Docker Compose
- **文件存储**: 本地存储 + 可选云存储

## 快速开始

### 使用Docker Compose（推荐）

1. 克隆项目
```bash
git clone <repository-url>
cd liveportrait-api
```

2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件配置数据库和其他设置
```

3. 启动服务
```bash
docker-compose up -d
```

4. 检查服务状态
```bash
# 检查API健康状态
curl http://localhost:8000/health

# 查看API文档
open http://localhost:8000/docs
```

### 手动安装

1. 环境要求
- Python 3.10+
- PostgreSQL 12+
- Redis 6+
- NVIDIA GPU (推荐)
- CUDA 11.8+ (GPU版本)

2. 安装依赖
```bash
# 运行安装脚本
chmod +x scripts/setup.sh
./scripts/setup.sh

# 或手动安装
pip install -r requirements.txt
```

3. 配置数据库
```bash
# 创建数据库表
python -c "from app.database import create_tables; create_tables()"
```

4. 启动服务
```bash
# 启动API服务器
uvicorn app.main:app --host 0.0.0.0 --port 8000

# 启动Celery工作进程
celery -A app.celery_app worker --loglevel=info

# 启动Celery监控（可选）
celery -A app.celery_app flower --port=5555
```

## API使用示例

### 1. 上传文件

```bash
# 上传源图片
curl -X POST "http://localhost:8000/api/v1/files/upload/image" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@source_image.jpg"

# 上传驱动视频
curl -X POST "http://localhost:8000/api/v1/files/upload/video" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@driving_video.mp4"
```

### 2. 创建人像动画任务

```bash
curl -X POST "http://localhost:8000/api/v1/generate/portrait" \
  -H "Content-Type: application/json" \
  -d '{
    "source_file_id": "source-file-uuid",
    "driving_file_id": "driving-file-uuid",
    "flag_stitching": true,
    "flag_relative": true
  }'
```

### 3. 查询任务状态

```bash
curl "http://localhost:8000/api/v1/tasks/{task_id}"
```

### 4. 下载结果

```bash
curl "http://localhost:8000/api/v1/files/{output_file_id}/download" \
  -o result.mp4
```

## API文档

启动服务后，访问以下地址查看完整API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 配置说明

主要配置项在 `.env` 文件中：

```env
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/liveportrait

# Redis配置
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/1

# 文件存储
UPLOAD_DIR=./uploads
OUTPUT_DIR=./outputs
MAX_FILE_SIZE=100MB

# 模型配置
MODEL_WEIGHTS_DIR=./pretrained_weights
DEVICE=cuda
```

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx         │    │   FastAPI       │    │   Celery        │
│   (Load         │────│   (API Server)  │────│   (Workers)     │
│   Balancer)     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │   Redis         │    │   File Storage  │
│   (Database)    │    │   (Cache/Queue) │    │   (Local/Cloud) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 监控和日志

- **任务监控**: Celery Flower - http://localhost:5555
- **API监控**: FastAPI自带的/health端点
- **日志**: 存储在 `./logs/` 目录下

## 故障排除

### 常见问题

1. **GPU内存不足**
   - 减少并发任务数量
   - 调整模型参数
   - 使用CPU模式（性能较低）

2. **模型加载失败**
   - 检查预训练权重是否下载完整
   - 确认CUDA版本兼容性
   - 查看日志文件获取详细错误信息

3. **任务处理缓慢**
   - 增加Celery工作进程数量
   - 使用更强的GPU
   - 优化输入文件大小

### 日志查看

```bash
# 查看API日志
tail -f logs/app.log

# 查看Celery日志
docker-compose logs -f celery_worker

# 查看所有服务日志
docker-compose logs -f
```

## 开发指南

### 项目结构

```
├── app/
│   ├── api/                 # API路由和端点
│   ├── models/              # 数据模型和AI模型
│   ├── services/            # 业务逻辑服务
│   ├── tasks/               # Celery任务
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接
│   └── main.py              # 主应用文件
├── scripts/                 # 部署和管理脚本
├── docker-compose.yml       # Docker编排文件
├── Dockerfile              # Docker镜像构建
├── requirements.txt        # Python依赖
└── README.md               # 项目文档
```

### 添加新功能

1. 在 `app/api/endpoints/` 中添加新的API端点
2. 在 `app/tasks/` 中添加新的Celery任务
3. 在 `app/models/` 中添加新的数据模型
4. 更新API文档和测试

## 许可证

本项目基于 MIT 许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至项目维护者
