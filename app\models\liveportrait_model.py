"""
LivePortrait model wrapper for video and image generation
"""
import os
import sys
import torch
import numpy as np
from typing import Optional, Dict, Any, Union
from pathlib import Path
import cv2
from PIL import Image
import subprocess
import tempfile

from app.config import settings
from app.logging_config import app_logger
from app.exceptions import ModelError


class LivePortraitModel:
    """LivePortrait model wrapper"""

    def __init__(self):
        self.device = settings.device
        self.model_weights_dir = Path(settings.model_weights_dir)
        self.model = None
        self.is_loaded = False
        self.liveportrait_repo_path = None

        # Try to find LivePortrait repository
        self._setup_liveportrait_path()
    
    def _setup_liveportrait_path(self):
        """Setup LivePortrait repository path"""
        # Check if we're in a LivePortrait environment
        possible_paths = [
            Path("./"),  # Current directory
            Path("../LivePortrait"),  # Parent directory
            Path("./LivePortrait"),  # Subdirectory
            self.model_weights_dir.parent,  # Model weights parent
        ]

        for path in possible_paths:
            if (path / "inference.py").exists() and (path / "src").exists():
                self.liveportrait_repo_path = path
                sys.path.insert(0, str(path))
                app_logger.info(f"Found LivePortrait repository at: {path}")
                break

        if not self.liveportrait_repo_path:
            app_logger.warning("LivePortrait repository not found, will use subprocess calls")

    def load_model(self) -> bool:
        """Load LivePortrait model"""
        try:
            app_logger.info("Initializing LivePortrait model...")

            # Check if model weights exist
            if not self.model_weights_dir.exists():
                raise ModelError(f"Model weights directory not found: {self.model_weights_dir}")

            # For now, we'll use subprocess calls to LivePortrait
            # This is more reliable than trying to import the modules directly
            self.is_loaded = True
            app_logger.info("LivePortrait model initialized successfully")
            return True

        except Exception as e:
            app_logger.error(f"Failed to initialize LivePortrait model: {e}")
            raise ModelError(f"Failed to initialize model: {e}")
    
    def generate_portrait_animation(
        self,
        source_image_path: str,
        driving_video_path: str,
        output_path: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate portrait animation from source image and driving video"""

        if not self.is_loaded:
            self.load_model()

        try:
            app_logger.info(f"Generating portrait animation: {source_image_path} -> {output_path}")

            # Validate input files
            if not os.path.exists(source_image_path):
                raise ModelError(f"Source image not found: {source_image_path}")

            if not os.path.exists(driving_video_path):
                raise ModelError(f"Driving video not found: {driving_video_path}")

            # Use subprocess to call LivePortrait inference
            cmd = self._build_inference_command(
                source_image_path,
                driving_video_path,
                output_path,
                **kwargs
            )

            app_logger.info(f"Running command: {' '.join(cmd)}")

            # Run the command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600,  # 10 minutes timeout
                cwd=self.liveportrait_repo_path
            )

            if result.returncode != 0:
                app_logger.error(f"LivePortrait command failed: {result.stderr}")
                raise ModelError(f"Generation failed: {result.stderr}")

            # Check if output file was created
            if not os.path.exists(output_path):
                raise ModelError("Output file was not created")

            # Get video metadata
            metadata = self._get_video_metadata(output_path)

            return {
                'success': True,
                'output_path': output_path,
                'metadata': {
                    'source_image': source_image_path,
                    'driving_video': driving_video_path,
                    **metadata
                }
            }

        except subprocess.TimeoutExpired:
            app_logger.error("Portrait animation generation timed out")
            raise ModelError("Generation timed out")
        except Exception as e:
            app_logger.error(f"Portrait animation generation failed: {e}")
            raise ModelError(f"Generation failed: {e}")
    
    def generate_animal_animation(
        self,
        source_image_path: str,
        driving_data_path: str,
        output_path: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate animal animation from source image and driving data"""
        
        if not self.is_loaded:
            self.load_model()
        
        try:
            app_logger.info(f"Generating animal animation: {source_image_path} -> {output_path}")
            
            # Import animal-specific modules
            try:
                from src.live_portrait_pipeline_animal import LivePortraitPipelineAnimal
            except ImportError as e:
                raise ModelError(f"Animal mode not supported: {e}")
            
            # Validate input files
            if not os.path.exists(source_image_path):
                raise ModelError(f"Source image not found: {source_image_path}")
            
            if not os.path.exists(driving_data_path):
                raise ModelError(f"Driving data not found: {driving_data_path}")
            
            # Configure animal pipeline
            animal_pipeline = LivePortraitPipelineAnimal(
                cfg=self.model.cfg,
                args=self.model.args
            )
            
            # Set parameters
            driving_multiplier = kwargs.get('driving_multiplier', 1.0)
            flag_stitching = kwargs.get('flag_stitching', False)
            
            # Generate animation
            result = animal_pipeline.execute(
                source_image_path,
                driving_data_path,
                driving_multiplier=driving_multiplier,
                flag_stitching=flag_stitching
            )
            
            # Save result
            if result and 'output_video' in result:
                import shutil
                shutil.move(result['output_video'], output_path)
                
                return {
                    'success': True,
                    'output_path': output_path,
                    'metadata': {
                        'source_image': source_image_path,
                        'driving_data': driving_data_path,
                        'driving_multiplier': driving_multiplier,
                        'duration': result.get('duration', 0),
                        'fps': result.get('fps', 25),
                        'resolution': result.get('resolution', [512, 512])
                    }
                }
            else:
                raise ModelError("Animal animation generation failed")
                
        except Exception as e:
            app_logger.error(f"Animal animation generation failed: {e}")
            raise ModelError(f"Generation failed: {e}")
    
    def health_check(self) -> Dict[str, Any]:
        """Check model health status"""
        try:
            return {
                'model_loaded': self.is_loaded,
                'device': self.device,
                'weights_dir': str(self.model_weights_dir),
                'weights_exist': self.model_weights_dir.exists(),
                'cuda_available': torch.cuda.is_available(),
                'gpu_memory': self._get_gpu_memory() if torch.cuda.is_available() else None
            }
        except Exception as e:
            app_logger.error(f"Health check failed: {e}")
            return {
                'model_loaded': False,
                'error': str(e)
            }
    
    def _get_gpu_memory(self) -> Dict[str, float]:
        """Get GPU memory information"""
        if not torch.cuda.is_available():
            return {}
        
        try:
            device_count = torch.cuda.device_count()
            memory_info = {}
            
            for i in range(device_count):
                total = torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB
                allocated = torch.cuda.memory_allocated(i) / 1024**3  # GB
                cached = torch.cuda.memory_reserved(i) / 1024**3  # GB
                
                memory_info[f'gpu_{i}'] = {
                    'total': round(total, 2),
                    'allocated': round(allocated, 2),
                    'cached': round(cached, 2),
                    'free': round(total - allocated, 2)
                }
            
            return memory_info
        except Exception:
            return {}

    def _build_inference_command(
        self,
        source_path: str,
        driving_path: str,
        output_path: str,
        **kwargs
    ) -> list:
        """Build command for LivePortrait inference"""

        if not self.liveportrait_repo_path:
            raise ModelError("LivePortrait repository not found")

        cmd = [
            "python",
            "inference.py",
            "-s", source_path,
            "-d", driving_path
        ]

        # Add optional parameters
        if kwargs.get('flag_lip_zero', False):
            cmd.append("--flag_lip_zero")

        if kwargs.get('flag_eye_retargeting', False):
            cmd.append("--flag_eye_retargeting")

        if not kwargs.get('flag_stitching', True):
            cmd.append("--no_flag_stitching")

        if not kwargs.get('flag_relative', True):
            cmd.append("--no_flag_relative")

        if not kwargs.get('flag_pasteback', True):
            cmd.append("--no_flag_pasteback")

        if kwargs.get('flag_crop_driving_video', False):
            cmd.append("--flag_crop_driving_video")

        return cmd

    def _get_video_metadata(self, video_path: str) -> Dict[str, Any]:
        """Get video metadata using ffprobe"""
        try:
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)

                video_stream = None
                for stream in data.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        video_stream = stream
                        break

                if video_stream:
                    return {
                        'duration': float(data.get('format', {}).get('duration', 0)),
                        'fps': eval(video_stream.get('r_frame_rate', '25/1')),
                        'resolution': [
                            video_stream.get('width', 512),
                            video_stream.get('height', 512)
                        ],
                        'codec': video_stream.get('codec_name', 'unknown')
                    }
        except Exception as e:
            app_logger.warning(f"Failed to get video metadata: {e}")

        return {
            'duration': 0,
            'fps': 25,
            'resolution': [512, 512],
            'codec': 'unknown'
        }


# Global model instance
liveportrait_model = LivePortraitModel()
