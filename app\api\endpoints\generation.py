"""
Generation API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.database import Task, TaskType, FileRecord
from app.api.schemas import (
    PortraitAnimationRequest, AnimalAnimationRequest, 
    GenerationResponse, TaskResponse
)
from app.tasks.generation_tasks import generate_portrait_animation, generate_animal_animation
from app.models.liveportrait_model import liveportrait_model
from app.logging_config import app_logger

router = APIRouter()


@router.post("/generate/portrait", response_model=GenerationResponse)
async def generate_portrait_animation_endpoint(
    request: PortraitAnimationRequest,
    db: Session = Depends(get_db)
):
    """Generate portrait animation"""
    try:
        # Validate source file
        source_file = db.query(FileRecord).filter(
            FileRecord.id == request.source_file_id
        ).first()
        
        if not source_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Source file not found"
            )
        
        if source_file.file_type != 'image':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Source file must be an image"
            )
        
        # Validate driving file
        driving_file = db.query(FileRecord).filter(
            FileRecord.id == request.driving_file_id
        ).first()
        
        if not driving_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Driving file not found"
            )
        
        if driving_file.file_type != 'video':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Driving file must be a video"
            )
        
        # Create task
        task = Task(
            task_type=TaskType.PORTRAIT_ANIMATION,
            source_file_id=request.source_file_id,
            driving_file_id=request.driving_file_id,
            parameters=request.dict(exclude={'source_file_id', 'driving_file_id'}),
            user_id=None  # TODO: Get from authentication
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        # Submit to Celery
        task_id = str(task.id)
        generate_portrait_animation.delay(
            task_id=task_id,
            source_file_id=request.source_file_id,
            driving_file_id=request.driving_file_id,
            parameters=request.dict(exclude={'source_file_id', 'driving_file_id'})
        )
        
        app_logger.info(f"Portrait animation task submitted: {task_id}")
        
        return GenerationResponse(
            task_id=task_id,
            status="pending",
            message="Portrait animation task submitted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error submitting portrait animation task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/generate/animal", response_model=GenerationResponse)
async def generate_animal_animation_endpoint(
    request: AnimalAnimationRequest,
    db: Session = Depends(get_db)
):
    """Generate animal animation"""
    try:
        # Validate source file
        source_file = db.query(FileRecord).filter(
            FileRecord.id == request.source_file_id
        ).first()
        
        if not source_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Source file not found"
            )
        
        if source_file.file_type != 'image':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Source file must be an image"
            )
        
        # Validate driving file
        driving_file = db.query(FileRecord).filter(
            FileRecord.id == request.driving_file_id
        ).first()
        
        if not driving_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Driving file not found"
            )
        
        # Create task
        task = Task(
            task_type=TaskType.ANIMAL_ANIMATION,
            source_file_id=request.source_file_id,
            driving_file_id=request.driving_file_id,
            parameters=request.dict(exclude={'source_file_id', 'driving_file_id'}),
            user_id=None  # TODO: Get from authentication
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        # Submit to Celery
        task_id = str(task.id)
        generate_animal_animation.delay(
            task_id=task_id,
            source_file_id=request.source_file_id,
            driving_file_id=request.driving_file_id,
            parameters=request.dict(exclude={'source_file_id', 'driving_file_id'})
        )
        
        app_logger.info(f"Animal animation task submitted: {task_id}")
        
        return GenerationResponse(
            task_id=task_id,
            status="pending",
            message="Animal animation task submitted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"Error submitting animal animation task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/models/status")
async def get_model_status():
    """Get model status"""
    try:
        health_status = liveportrait_model.health_check()
        
        return {
            "status": "healthy" if health_status.get("model_loaded", False) else "unhealthy",
            "model_info": health_status
        }
        
    except Exception as e:
        app_logger.error(f"Error getting model status: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
