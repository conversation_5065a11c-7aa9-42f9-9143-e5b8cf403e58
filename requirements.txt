# FastAPI and web framework dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
jinja2==3.1.2

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis and caching
redis==5.0.1
hiredis==2.2.3

# Task queue
celery==5.3.4
flower==2.0.1

# File handling and storage
aiofiles==23.2.1
pillow==10.1.0
opencv-python==********
ffmpeg-python==0.2.0

# AI/ML dependencies for LivePortrait
torch==2.1.1
torchvision==0.16.1
torchaudio==2.1.1
transformers==4.35.2
diffusers==0.24.0
accelerate==0.24.1
xformers==0.0.22.post7
insightface==0.7.3
onnxruntime==1.16.3
opencv-contrib-python==********
imageio==2.31.6
imageio-ffmpeg==0.4.9
scikit-image==0.22.0
scipy==1.11.4
numpy==1.24.4
matplotlib==3.8.2

# Hugging Face integration
huggingface-hub==0.19.4
datasets==2.14.7

# Configuration and environment
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# Logging and monitoring
loguru==0.7.2
prometheus-client==0.19.0

# Security and authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Utilities
click==8.1.7
tqdm==4.66.1
rich==13.7.0
typer==0.9.0

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Production server
gunicorn==21.2.0
