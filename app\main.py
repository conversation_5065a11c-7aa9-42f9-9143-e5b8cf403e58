"""
FastAPI main application
"""
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from contextlib import asynccontextmanager

from app.config import settings
from app.logging_config import app_logger
from app.database import create_tables
from app.exceptions import LivePortraitException
from app.api.endpoints import files, tasks, generation
from app.api.schemas import HealthCheckResponse, ErrorResponse


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    app_logger.info("Starting LivePortrait API...")
    
    # Create database tables
    try:
        create_tables()
        app_logger.info("Database tables created successfully")
    except Exception as e:
        app_logger.error(f"Failed to create database tables: {e}")
    
    # Initialize model (optional, can be lazy loaded)
    try:
        from app.models.liveportrait_model import liveportrait_model
        # liveportrait_model.load_model()  # Uncomment for eager loading
        app_logger.info("LivePortrait model initialized")
    except Exception as e:
        app_logger.warning(f"Failed to initialize model: {e}")
    
    yield
    
    # Shutdown
    app_logger.info("Shutting down LivePortrait API...")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="LivePortrait API for video and image generation using Kuaishou's LivePortrait model",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)


# Exception handlers
@app.exception_handler(LivePortraitException)
async def liveportrait_exception_handler(request: Request, exc: LivePortraitException):
    """Handle custom LivePortrait exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.__class__.__name__,
            message=exc.message,
            details=exc.details,
            timestamp=datetime.utcnow()
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    app_logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="InternalServerError",
            message="An unexpected error occurred",
            details={},
            timestamp=datetime.utcnow()
        ).dict()
    )


# Health check endpoint
@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint"""
    try:
        from app.models.liveportrait_model import liveportrait_model
        model_status = liveportrait_model.health_check()
    except Exception as e:
        app_logger.warning(f"Model health check failed: {e}")
        model_status = {"error": str(e)}
    
    return HealthCheckResponse(
        status="healthy",
        timestamp=datetime.utcnow(),
        version=settings.app_version,
        model_status=model_status
    )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to LivePortrait API",
        "version": settings.app_version,
        "docs": "/docs",
        "health": "/health"
    }


# Include API routers
app.include_router(
    files.router,
    prefix=f"{settings.api_v1_str}/files",
    tags=["files"]
)

app.include_router(
    tasks.router,
    prefix=f"{settings.api_v1_str}/tasks",
    tags=["tasks"]
)

app.include_router(
    generation.router,
    prefix=f"{settings.api_v1_str}/generate",
    tags=["generation"]
)


# Additional endpoints for monitoring
@app.get("/metrics")
async def metrics():
    """Metrics endpoint for monitoring"""
    if not settings.enable_metrics:
        raise HTTPException(status_code=404, detail="Metrics not enabled")
    
    # TODO: Implement Prometheus metrics
    return {"message": "Metrics endpoint - TODO: Implement Prometheus metrics"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug
    )
